[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "prompt_generator"
version = "0.1.0"
authors = [{ name = "Your Name", email = "<EMAIL>" }]
description = "A custom prompt generator for hedge fund analysis"
requires-python = ">=3.9"
dependencies = [
  "langchain>=0.3.26",
  "langchain-openai>=0.3.27",
  "openai>=1.0.0",
  "pydantic>=2.0.0",
  "python-dotenv>=1.1.1",
  "rich>=10.0.0",
  "typer>=0.9.0",
]

[project.scripts]
prompt-generator = "prompt_generator.cli:main"

[tool.setuptools.packages.find]
include = ["prompt_generator*"]
exclude = ["static*", "templates*", "venv*", "*.egg-info*"]

[tool.black]
line-length = 150

[tool.ruff]
line-length = 150

[tool.ruff.lint]
select = [
  "E",
  "F",
  "W",
  "C90",
  "I",
  "N",
  "D",
  "UP",
  "YTT",
  "ANN",
  "S",
  "BLE",
  "FBT",
  "B",
  "A",
  "COM",
  "C4",
  "DTZ",
  "T10",
  "EM",
  "EXE",
  "ISC",
  "ICN",
  "G",
  "INP",
  "PIE",
  "T20",
  "PYI",
  "PT",
  "Q",
  "RSE",
  "RET",
  "SLF",
  "SIM",
  "TID",
  "TCH",
  "INT",
  "ARG",
  "PTH",
  "ERA",
  "PD",
  "PGH",
  "PL",
  "TRY",
  "NPY",
  "RUF",
]
ignore = [
  "D100",
  "D101",
  "D102",
  "D103",
  "D104",
  "D105",
  "D106",
  "D107",
] # Ignore missing docstring warnings for now

[tool.isort]
profile = "black"
line_length = 150

[dependency-groups]
dev = ["ruff>=0.12.2"]
