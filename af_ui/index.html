<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AF Chat SPA</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body { margin: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; }
    </style>
</head>
<body>
    <div id="root"></div>
    <script type="text/babel" data-type="module">
        // Import statements need to be converted for browser use
        const { useState } = React;
        
        // Lucide icons as simple components
        const ChevronDown = () => <span>▼</span>;
        const ChevronUp = () => <span>▲</span>;
        const ChevronRight = () => <span>▶</span>;
        const Info = () => <span>ℹ</span>;
        const X = () => <span>✕</span>;
    </script>
    <script type="text/babel" src="./af_chat_spa.js"></script>
</body>
</html>