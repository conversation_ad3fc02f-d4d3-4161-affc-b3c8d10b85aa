#!/usr/bin/env python3
"""
Simple screenshot tool using macOS screencapture and Chrome
"""
import subprocess
import time
import os
import sys
from datetime import datetime

def take_screenshot(port="8080"):
    """Open browser and take screenshot using macOS tools"""
    
    url = f"http://localhost:{port}/"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    screenshot_path = f"screenshots/screenshot_{timestamp}.png"
    
    # Ensure directory exists
    os.makedirs("screenshots", exist_ok=True)
    
    # Open URL in Chrome
    print(f"Opening {url} in Chrome...")
    subprocess.run(["open", "-a", "Google Chrome", url])
    
    # Wait for page to load
    time.sleep(3)
    
    # Take screenshot using screencapture
    print(f"Taking screenshot...")
    subprocess.run(["screencapture", "-x", screenshot_path])
    
    print(f"Screenshot saved to: {screenshot_path}")
    return screenshot_path

if __name__ == "__main__":
    port = sys.argv[1] if len(sys.argv) > 1 else "8080"
    take_screenshot(port)