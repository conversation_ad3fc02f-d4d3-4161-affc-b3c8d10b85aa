# Data Mapping Analysis: SPA vs step_combined.json

## Overview
This document analyzes the data structure expectations of the af_chat_spa.js React application and maps them to the available data in step_combined.json for creating a single task card visualization.

## 1. What the SPA Expects (from proxy/dummy data)

### Task Card Structure
```javascript
{
  id: 'task-001',
  name: 'User Authentication Flow Validation',
  context: 'prod - AWS us-east-1',
  systemPrompt: 'Validate all authentication endpoints...',
  testScenarios: [
    {
      id: 'test-001',
      description: 'Calculate compound interest correctly',
      status: 'pass' | 'fail',
      output: 'string describing output',
      input: { /* object with test inputs */ },
      graderScores: [
        {
          criterion: 'string',
          score: number (0-100),
          status: 'pass' | 'fail' | 'warn'
        }
      ]
    }
  ],
  aggregateScores: [
    {
      category: 'string',
      passRate: number (0-100)
    }
  ]
}
```

### Key UI Elements Expecting Data:
1. **Task Name** - Short, descriptive name of the task
2. **Context** - Environment/configuration info
3. **System Prompt** - Detailed instructions/requirements
4. **Test Scenarios** - List of individual tests with:
   - Description
   - Pass/fail status
   - Input parameters
   - Output result
   - Grader scores with criteria
5. **Aggregate Scores** - Overall performance by category with pass rates

## 2. What step_combined.json Provides

### Available Data Structure:
```json
{
  "step_by_step": {
    "sections": [
      {
        "name": "INITIAL INPUT",
        "data": "user's original prompt"
      },
      {
        "name": "STEP 1: REQUIREMENTS DOCUMENT GENERATOR",
        "data": {
          "requirements_doc": {
            "problem_statement": "...",
            "core_objectives": [...],
            "key_requirements": [...],
            "success_criteria": [...],
            "quality_metrics": {...}
          }
        }
      },
      {
        "name": "STEP 2: PROMPT GENERATOR",
        "data": {
          "generated_prompt": {
            "system_message": "...",
            "user_message": "...",
            "metadata": {...}
          }
        }
      },
      {
        "name": "STEP 7: TEST CASE CHUNKER",
        "data": {
          "chunked_cases": {
            "test_cases": [
              {
                "seed_id": "seed_1",
                "system_message": "...",
                "user_message": "...",
                "seed_data": {
                  "expected_output": "...",
                  "reasoning": "..."
                }
              }
            ]
          }
        }
      }
    ]
  },
  "results": {
    "initial_prompt": "...",
    "final_prompt": {
      "system_message": "...",
      "user_message": "..."
    },
    "final_requirements": {...}
  }
}
```

## 3. Mapping Strategy

### Task Card Mapping:

| SPA Field | step_combined.json Source | Mapping Logic |
|-----------|---------------------------|---------------|
| `id` | Generate from filename or timestamp | `"task-" + timestamp` or extract from metadata |
| `name` | `results.initial_prompt` (truncated) | First 50 chars of initial prompt or extract key objective |
| `context` | `results.final_prompt.metadata.domain` + environment info | Combine domain + any deployment/environment data |
| `systemPrompt` | `results.final_prompt.system_message` | Direct mapping |

### Test Scenarios Mapping:

| SPA Field | step_combined.json Source | Mapping Logic |
|-----------|---------------------------|---------------|
| `testScenarios[].id` | `test_cases[].seed_id` | Direct mapping |
| `testScenarios[].description` | `test_cases[].seed_data.reasoning` | Truncate to first sentence |
| `testScenarios[].status` | Not directly available | Default to 'pending' or derive from validation |
| `testScenarios[].output` | `test_cases[].seed_data.expected_output` | Truncate if needed |
| `testScenarios[].input` | Parse from `test_cases[].user_message` | Extract placeholders and values |
| `testScenarios[].graderScores` | Generate from quality metrics | Map quality dimensions to scores |

### Aggregate Scores Mapping:

| SPA Field | step_combined.json Source | Mapping Logic |
|-----------|---------------------------|---------------|
| `aggregateScores[].category` | Extract from various sections | Categories from requirements, quality metrics |
| `aggregateScores[].passRate` | Calculate from quality scores | Convert quality metrics to percentages |

## 4. Implementation Code Example

```javascript
function mapStepCombinedToTaskCard(stepData) {
  const results = stepData.results;
  const testCases = stepData.step_by_step.sections
    .find(s => s.name.includes("TEST CASE CHUNKER"))
    ?.data?.chunked_cases?.test_cases || [];

  return {
    id: `task-${Date.now()}`,
    name: results.initial_prompt.substring(0, 50) + '...',
    context: `${results.final_prompt.metadata.domain} - ${results.final_prompt.metadata.tone}`,
    systemPrompt: results.final_prompt.system_message,
    
    testScenarios: testCases.map((tc, idx) => ({
      id: tc.seed_id,
      description: tc.seed_data.reasoning.split('.')[0],
      status: tc.metadata?.validation?.is_valid ? 'pass' : 'fail',
      output: tc.seed_data.expected_output.substring(0, 100),
      input: extractPlaceholders(tc.user_message),
      graderScores: generateGraderScores(tc.seed_data.quality_metrics)
    })),
    
    aggregateScores: generateAggregateScores(stepData)
  };
}

function extractPlaceholders(userMessage) {
  const placeholders = {};
  const matches = userMessage.match(/\{\{(\w+)\}\}/g) || [];
  matches.forEach(match => {
    const key = match.replace(/[{}]/g, '');
    placeholders[key] = 'placeholder_value';
  });
  return placeholders;
}

function generateGraderScores(qualityMetrics) {
  return [
    {
      criterion: 'Complexity',
      score: qualityMetrics?.complexity === 'moderate' ? 75 : 50,
      status: 'pass'
    },
    {
      criterion: 'Relevance',
      score: (qualityMetrics?.relevance || 0.5) * 100,
      status: qualityMetrics?.relevance > 0.8 ? 'pass' : 'warn'
    },
    {
      criterion: 'Uniqueness',
      score: (qualityMetrics?.uniqueness || 0.5) * 100,
      status: qualityMetrics?.uniqueness > 0.8 ? 'pass' : 'warn'
    }
  ];
}

function generateAggregateScores(stepData) {
  const categories = [
    'Requirements Clarity',
    'Prompt Quality',
    'Test Coverage',
    'Validation Success'
  ];
  
  return categories.map(cat => ({
    category: cat,
    passRate: Math.random() * 40 + 60 // Placeholder - implement actual logic
  }));
}
```

## 5. Gaps and Mismatches

### Missing in step_combined.json:
1. **Actual test execution results** - The data contains test definitions but not execution results
2. **Real-time performance metrics** - No actual runtime data or performance measurements
3. **Specific grader criteria scores** - Quality metrics exist but not in the expected grader format
4. **Test execution status** - Only validation status available, not pass/fail from execution

### Data Transformation Required:
1. **Status inference** - Must derive test status from validation data
2. **Score calculation** - Need to convert quality metrics (0-1 scale) to percentage scores
3. **Category generation** - Aggregate categories need to be extracted from various sections
4. **Input parsing** - Test inputs must be extracted from templated messages

### Recommendations:
1. **Enhance data collection** - Capture actual test execution results
2. **Add scoring metadata** - Include explicit grader criteria and scores
3. **Standardize status fields** - Use consistent pass/fail/warn status across all data
4. **Include execution metrics** - Add timing, memory usage, and other performance data

## 6. Validation Checks

If the SPA expects:
- ✓ Task identification (id, name) → Can be derived from initial prompt and metadata
- ✓ System configuration (systemPrompt) → Available in final_prompt
- ✓ Test definitions → Available in test_cases
- ✗ Test execution results → Not available, must be mocked or generated
- ✗ Real grader scores → Must be calculated from quality metrics
- ✓ Quality metrics → Available but need transformation

## Conclusion

The step_combined.json file contains rich data about prompt engineering workflow but lacks the specific test execution and grading data that the SPA expects. A mapping layer can transform most of the available data, but some fields will need to be:
1. Generated based on available metrics
2. Defaulted to reasonable values
3. Enhanced in future data collection

The mapping is feasible but requires significant data transformation and some creative interpretation of the available fields.