# Data Pipeline Summary: step_combined.json → SPA Task Card

## What to Pipe Where

### 1. **Basic Task Information**
- **Source**: `step_combined.sections[0].output` (INITIAL INPUT)
- **Target**: `taskCard.name`
- **Transform**: First 50 chars + "..."

### 2. **System Prompt**
- **Source**: `step_combined.sections[2].data.generated_prompt.system_message`
- **Target**: `taskCard.systemPrompt`
- **Transform**: Direct copy

### 3. **Context**
- **Source**: `step_combined.sections[2].data.generated_prompt.metadata`
- **Target**: `taskCard.context`
- **Transform**: `${domain} - ${tone}`

### 4. **Test Scenarios**
- **Source**: `step_combined.sections[3].data` (STEP 3: TEST SCENARIOS)
- **Target**: `taskCard.testScenarios[]`
- **Transform**: 
  - Extract each test scenario
  - Map quality_metrics to graderScores (×100 for percentages)
  - Extract placeholders from reasoning text
  - Set status based on validation results

### 5. **Grader Scores Per Test**
- **Source**: Test scenario quality_metrics (complexity, relevance, uniqueness)
- **Target**: `testScenario.graderScores[]`
- **Transform**: Create array of {criterion, score, status} objects

### 6. **Aggregate Scores**
- **Source**: Various quality scores throughout step_combined
- **Target**: `taskCard.aggregateScores[]`
- **Transform**: Generate 10 categories with passRate calculations

## Data Flow Diagram

```
step_combined.json                    →  Mapping Script  →    SPA Task Card
├─ Initial Input                      →  name           →    name
├─ System/User Messages               →  systemPrompt   →    systemPrompt  
├─ Domain/Tone Metadata               →  context        →    context
├─ Test Scenarios                     →  testScenarios  →    testScenarios[]
│  ├─ Input/Expected                  →  description    →    ├─ description
│  ├─ Quality Metrics                 →  graderScores   →    ├─ graderScores[]
│  └─ Validation Results              →  status         →    └─ status
└─ Quality/Alignment Scores           →  aggregateScores→    aggregateScores[]
```

## Key Transformations

1. **Score Scaling**: All 0-1 scores → 0-100 percentages
2. **Status Inference**: quality_score > 0.7 → "pass", else "fail"
3. **Placeholder Extraction**: Parse test reasoning for {{variables}}
4. **Aggregate Generation**: Create 10 categories from available metrics

## What This Represents

This data mapping represents **a single task card** showing:
- The original task request (Industry 101 document creation)
- The refined system prompt for execution
- Test scenarios to validate output quality
- Individual and aggregate quality scores
- Pass/fail status for each validation

The task card provides a complete view of one pipeline execution, suitable for display in the UI's task management interface.