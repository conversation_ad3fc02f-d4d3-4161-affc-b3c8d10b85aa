{
    // Python configuration
    "python.defaultInterpreterPath": ".venv/bin/python",
    "[python]": {
        "editor.defaultFormatter": "charliermarsh.ruff",
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
            "source.fixAll.ruff": "explicit",
            "source.organizeImports.ruff": "explicit"
        }
    },
    
    // Notebook configuration
    "notebook.formatOnSave.enabled": true,
    "notebook.codeActionsOnSave": {
        "source.fixAll": "explicit",
        "source.organizeImports": "explicit"
    },
    
    // Ruff configuration (v2024+ native server)
    "ruff.enable": true,
    "ruff.configuration": "pyproject.toml",
    
    // Python language server configuration (Cursor Pyright disabled)
    // "cursorpyright.analysis.typeCheckingMode": "strict",
    // "cursorpyright.analysis.inlayHints.variableTypes": true,
    // "cursorpyright.analysis.inlayHints.functionReturnTypes": true,
    
    // File exclusions
    "files.exclude": {
        "uv.lock": true,
        ".archive": true,
        ".ruff_cache": true,
        ".venv": true,
        "**/.venv": true,
        ".mypy_cache": true,
        ".ruffignore": true,
        "requirements.txt": true,
        ".pytest_cache": true,
        ".cursorignore": true,
        ".coverage": true,
        ".sourcery.yaml": false,
        "src/pfc/__init__.py": true,
        "src/pfc/core/__init__.py": true,
        "**__init__.py": true,
        "src/pfc/doc_gen/__init__.py": true,
        ".sourcery": true,
        ".github": true,
        "src/pfc/__pycache__": true,
        "src/pfc/core/__pycache__": true,
        "src/pfc/core/utils/__pycache__": true,
        "src/pfc/core/utils/adapters/__init__.py": true,
        "src/pfc/core/utils/__init__.py": true,
        "src/pfc/core/llms/__pycache__": true,
        "_tmp/af3/__init__.py": true,
        "_tmp/af3/__pycache__": true,
        
        // Performance optimizations - exclude large temp/build directories
        "_tmp/af3/chat-ui/node_modules": true,
        "_tmp/af3/chat_ui1/frontend/.next": true,
        "_tmp/af3/chat_ui1/frontend/node_modules": true,
        "**/.next": true,
        "**/node_modules": true,
        "**/*.log": true,
        "**/*.cache": true,
        "**/*.pack": true,
        "**/*.gz": true
    },
    
    // Code Canvas configuration
    "codeCanvas.excludePatterns": [
        ".venv",
        "**/.venv",
        ".venv/**",
        "**/node_modules/**",
        "**/__pycache__/**",
        "**/.git/**"
    ],
    "codeCanvas.showAllFiles": true,
    "codeCanvas.defaultView": "all",
    "codeCanvas.filterMode": "all",
    
    // Search exclusions (some extensions may use search patterns)
    "search.exclude": {
        ".venv": true,
        "**/.venv": true,
        "**/node_modules": true,
        "**/__pycache__": true,
        "**/.git": true
    },
    
    // Colorize extension exclusions (prevent performance issues)
    "colorize.exclude": [
        "**/_tmp/**",
        "**/node_modules/**",
        "**/.next/**",
        "**/.venv/**",
        "**/__pycache__/**",
        "**/.git/**"
    ]
}